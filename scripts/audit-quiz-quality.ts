#!/usr/bin/env tsx

/**
 * Audit Quiz Quality - Identify Generic Content Issues
 * 
 * This script audits all quiz files to identify generic or low-quality content
 */

import { readdirSync, readFileSync } from 'fs';
import { join } from 'path';

interface QualityIssue {
  file: string;
  quiz_id: string;
  title: string;
  issues: string[];
  questionCount: number;
  sampleQuestions: string[];
}

function auditQuizQuality() {
  console.log('🔍 Auditing Quiz Quality...');
  console.log('📋 Checking for generic content and quality issues');
  
  const dataDir = join(process.cwd(), 'src', 'data');
  const quizFiles = readdirSync(dataDir).filter(file => file.endsWith('.json'));
  
  const qualityIssues: QualityIssue[] = [];
  let totalQuizzes = 0;
  let issueCount = 0;
  
  for (const filename of quizFiles) {
    const filePath = join(dataDir, filename);
    
    try {
      const fileContent = readFileSync(filePath, 'utf-8');
      const quizData = JSON.parse(fileContent);
      
      const metadata = quizData.quiz.metadata;
      const questions = quizData.quiz.questions || [];
      
      totalQuizzes++;
      
      const issues: string[] = [];
      const sampleQuestions: string[] = [];
      
      // Check for generic question patterns
      questions.forEach((q: any, index: number) => {
        if (index < 3) { // Sample first 3 questions
          sampleQuestions.push(q.text);
        }
        
        // Check for generic question text patterns
        if (q.text.includes('Question ' + (index + 1) + ':')) {
          issues.push('Generic question numbering pattern');
        }
        
        if (q.text.includes('with practical implementation scenarios') ||
            q.text.includes('with practical scenarios') ||
            q.text.includes('with real-world scenarios')) {
          issues.push('Generic scenario description');
        }
        
        // Check for template-style answers
        if (q.options) {
          const optionTexts = q.options.map((opt: any) => opt.text);
          if (optionTexts.includes('Correct security approach') ||
              optionTexts.includes('Suboptimal method') ||
              optionTexts.includes('Vulnerable implementation') ||
              optionTexts.includes('Incorrect technique')) {
            issues.push('Generic answer options');
          }
        }
        
        // Check for template explanations
        if (q.explanation && q.explanation.includes('This question covers professional')) {
          issues.push('Template explanation text');
        }
        
        // Check for very short or generic question text
        if (q.text.length < 50) {
          issues.push('Very short question text');
        }
        
        // Check for missing practical details
        if (!q.text.includes('IP') && !q.text.includes('server') && 
            !q.text.includes('attack') && !q.text.includes('vulnerability') &&
            !q.text.includes('log') && !q.text.includes('alert') &&
            q.type !== 'short_answer') {
          issues.push('Lacks specific technical details');
        }
      });
      
      // Remove duplicates
      const uniqueIssues = [...new Set(issues)];
      
      if (uniqueIssues.length > 0) {
        qualityIssues.push({
          file: filename,
          quiz_id: metadata.quiz_id,
          title: metadata.title,
          issues: uniqueIssues,
          questionCount: questions.length,
          sampleQuestions: sampleQuestions
        });
        issueCount++;
      }
      
    } catch (error) {
      console.error(`❌ Error processing ${filename}:`, error);
    }
  }
  
  console.log(`\n📊 Audit Results:`);
  console.log(`   Total quizzes: ${totalQuizzes}`);
  console.log(`   Quizzes with issues: ${issueCount}`);
  console.log(`   Quality score: ${Math.round(((totalQuizzes - issueCount) / totalQuizzes) * 100)}%`);
  
  if (qualityIssues.length > 0) {
    console.log(`\n⚠️  Quality Issues Found:`);
    
    // Sort by number of issues (most problematic first)
    qualityIssues.sort((a, b) => b.issues.length - a.issues.length);
    
    qualityIssues.forEach((issue, index) => {
      console.log(`\n${index + 1}. ${issue.title}`);
      console.log(`   File: ${issue.file}`);
      console.log(`   Quiz ID: ${issue.quiz_id}`);
      console.log(`   Questions: ${issue.questionCount}`);
      console.log(`   Issues: ${issue.issues.join(', ')}`);
      
      if (issue.sampleQuestions.length > 0) {
        console.log(`   Sample Questions:`);
        issue.sampleQuestions.forEach((q, qIndex) => {
          const preview = q.length > 100 ? q.substring(0, 100) + '...' : q;
          console.log(`     ${qIndex + 1}. ${preview}`);
        });
      }
    });
    
    console.log(`\n🔧 Recommendations:`);
    console.log(`1. Fix quizzes with "Generic answer options" - these need real scenarios`);
    console.log(`2. Replace "Template explanation text" with detailed technical explanations`);
    console.log(`3. Add specific technical details to questions lacking them`);
    console.log(`4. Remove generic numbering patterns and scenario descriptions`);
    
    // Identify the most problematic quizzes
    const highPriorityFixes = qualityIssues.filter(issue => 
      issue.issues.includes('Generic answer options') || 
      issue.issues.includes('Template explanation text')
    );
    
    if (highPriorityFixes.length > 0) {
      console.log(`\n🚨 High Priority Fixes Needed (${highPriorityFixes.length} quizzes):`);
      highPriorityFixes.forEach(issue => {
        console.log(`   - ${issue.title} (${issue.file})`);
      });
    }
    
  } else {
    console.log(`\n✅ All quizzes passed quality audit!`);
  }
}

// Run the audit
if (require.main === module) {
  auditQuizQuality();
}

export default auditQuizQuality;
