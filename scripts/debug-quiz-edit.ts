#!/usr/bin/env tsx

/**
 * Debug Quiz Edit Issues
 * 
 * This script helps diagnose issues with quiz editing by checking:
 * - Quiz existence and ownership
 * - User permissions
 * - Database relationships
 */

import { PrismaClient } from '@/generated/prisma';

const prisma = new PrismaClient();

async function debugQuizEdit() {
  console.log('🔍 Debugging Quiz Edit Issues...');
  
  try {
    const quizId = '6833d1f08ab21e4a7d974a8f';
    
    // Check if quiz exists
    console.log(`\n📋 Checking quiz: ${quizId}`);
    
    const quiz = await prisma.quiz.findUnique({
      where: { id: quizId },
      include: {
        creator: {
          select: {
            id: true,
            email: true,
            name: true,
            role: true
          }
        },
        questions: {
          select: {
            id: true,
            questionId: true,
            type: true,
            text: true
          },
          take: 3 // Just show first 3 questions
        },
        _count: {
          select: {
            questions: true,
            questionPools: true
          }
        }
      }
    });

    if (!quiz) {
      console.log('❌ Quiz not found!');
      
      // Check if it's an invalid ObjectId
      console.log('\n🔍 Checking if quiz ID format is valid...');
      if (quizId.length !== 24) {
        console.log('❌ Invalid ObjectId format (should be 24 characters)');
      } else {
        console.log('✅ ObjectId format is valid');
      }
      
      // Show some existing quizzes
      console.log('\n📋 Available quizzes:');
      const existingQuizzes = await prisma.quiz.findMany({
        select: {
          id: true,
          title: true,
          creatorId: true,
          isPublished: true
        },
        take: 10
      });
      
      existingQuizzes.forEach(q => {
        console.log(`  - ${q.id}: ${q.title} (Creator: ${q.creatorId}, Published: ${q.isPublished})`);
      });
      
      return;
    }

    console.log('✅ Quiz found!');
    console.log(`   Title: ${quiz.title}`);
    console.log(`   Description: ${quiz.description || 'No description'}`);
    console.log(`   Creator ID: ${quiz.creatorId}`);
    console.log(`   Published: ${quiz.isPublished}`);
    console.log(`   Questions: ${quiz._count.questions}`);
    console.log(`   Question Pools: ${quiz._count.questionPools}`);

    // Check creator details
    if (quiz.creator) {
      console.log('\n👤 Creator Details:');
      console.log(`   ID: ${quiz.creator.id}`);
      console.log(`   Email: ${quiz.creator.email}`);
      console.log(`   Name: ${quiz.creator.name}`);
      console.log(`   Role: ${quiz.creator.role}`);
    } else {
      console.log('\n❌ No creator found for this quiz!');
    }

    // Show sample questions
    if (quiz.questions.length > 0) {
      console.log('\n📝 Sample Questions:');
      quiz.questions.forEach((q, index) => {
        console.log(`   ${index + 1}. ${q.questionId} (${q.type})`);
        console.log(`      Text: ${typeof q.text === 'string' ? q.text.substring(0, 100) : JSON.stringify(q.text).substring(0, 100)}...`);
      });
    }

    // Check all users
    console.log('\n👥 All Users:');
    const users = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        _count: {
          select: {
            createdQuizzes: true
          }
        }
      }
    });

    users.forEach(user => {
      console.log(`   - ${user.id}: ${user.email} (${user.role}) - ${user._count.createdQuizzes} quizzes`);
    });

    // Check if there are any admin users
    const adminUsers = users.filter(u => u.role === 'admin');
    console.log(`\n🔑 Admin Users: ${adminUsers.length}`);
    
    if (adminUsers.length === 0) {
      console.log('❌ No admin users found! This could be the issue.');
      console.log('💡 You may need to create an admin user or update an existing user to admin role.');
    }

    // Check quiz ownership issues
    if (quiz.creatorId && !quiz.creator) {
      console.log('\n⚠️  Quiz has creatorId but creator not found - orphaned quiz!');
    }

    // Recommendations
    console.log('\n💡 Recommendations:');
    
    if (!quiz.creator) {
      console.log('   1. Quiz has no creator - assign it to an admin user');
    }
    
    if (adminUsers.length === 0) {
      console.log('   2. Create an admin user or promote existing user to admin');
    }
    
    if (quiz.creator && quiz.creator.role !== 'admin') {
      console.log('   3. Creator is not an admin - only admins can edit quizzes');
    }

    console.log('\n🔧 Potential Fixes:');
    console.log('   1. Update quiz creator to admin user:');
    console.log(`      UPDATE Quiz SET creatorId = 'admin_user_id' WHERE id = '${quizId}'`);
    console.log('   2. Promote user to admin:');
    console.log(`      UPDATE User SET role = 'admin' WHERE email = '<EMAIL>'`);

  } catch (error) {
    console.error('❌ Error during debugging:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the debug function
if (require.main === module) {
  debugQuizEdit();
}

export default debugQuizEdit;
