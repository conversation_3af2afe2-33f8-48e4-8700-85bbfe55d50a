#!/usr/bin/env tsx

/**
 * Comprehensive Placeholder Content Audit for QuizFlow
 * 
 * This script performs a detailed analysis of all quiz files to identify:
 * - Generic question patterns
 * - Template text
 * - Placeholder content
 * - Non-specific scenarios
 */

import { readFileSync, readdirSync, statSync } from 'fs';
import { join } from 'path';

interface PlaceholderIssue {
  type: 'generic_numbering' | 'template_text' | 'generic_options' | 'placeholder_scenario' | 'repeated_content';
  severity: 'high' | 'medium' | 'low';
  description: string;
  location: string;
  suggestion: string;
}

interface QuizAuditResult {
  filename: string;
  quizId: string;
  title: string;
  questionCount: number;
  issues: PlaceholderIssue[];
  qualityScore: number;
  needsReplacement: boolean;
}

const PLACEHOLDER_PATTERNS = {
  // Generic question numbering
  genericNumbering: [
    /Question \d+:/,
    /Question \d+ of \d+/,
    /\w+ Question \d+:/
  ],
  
  // Template explanation text
  templateText: [
    /This question covers professional/,
    /Industry best practices/,
    /Practical implementation techniques/,
    /Real-world security challenges/,
    /Advanced technical concepts/,
    /Understanding these concepts is essential/
  ],
  
  // Generic answer options
  genericOptions: [
    /Correct security approach/,
    /Suboptimal method/,
    /Vulnerable implementation/,
    /Incorrect technique/,
    /Option A for question/,
    /Option B for question/,
    /best approach to handle this security challenge/
  ],
  
  // Placeholder scenarios
  placeholderScenarios: [
    /with practical implementation scenarios/,
    /Practical cybersecurity scenario/,
    /practical implementation and real-world scenarios/,
    /You are conducting a security assessment and encounter a scenario/
  ],
  
  // Repeated content indicators
  repeatedContent: [
    /Security by design/,
    /Defense in depth/,
    /Risk assessment/,
    /Continuous monitoring/
  ]
};

function analyzeQuizFile(filePath: string): QuizAuditResult | null {
  try {
    const content = readFileSync(filePath, 'utf-8');
    const quiz = JSON.parse(content);
    
    if (!quiz.quiz || !quiz.quiz.metadata || !quiz.quiz.questions) {
      return null;
    }
    
    const issues: PlaceholderIssue[] = [];
    const metadata = quiz.quiz.metadata;
    const questions = quiz.quiz.questions;
    
    // Check each question for placeholder patterns
    questions.forEach((question: any, index: number) => {
      const questionText = question.text || '';
      const explanation = question.explanation || '';
      const options = question.options || [];
      
      // Check for generic numbering
      PLACEHOLDER_PATTERNS.genericNumbering.forEach(pattern => {
        if (pattern.test(questionText)) {
          issues.push({
            type: 'generic_numbering',
            severity: 'high',
            description: 'Generic question numbering pattern detected',
            location: `Question ${index + 1}: "${questionText.substring(0, 50)}..."`,
            suggestion: 'Replace with specific, descriptive question text'
          });
        }
      });
      
      // Check for template text
      PLACEHOLDER_PATTERNS.templateText.forEach(pattern => {
        if (pattern.test(explanation)) {
          issues.push({
            type: 'template_text',
            severity: 'high',
            description: 'Template explanation text found',
            location: `Question ${index + 1} explanation`,
            suggestion: 'Replace with specific technical explanation'
          });
        }
      });
      
      // Check for generic options
      options.forEach((option: any, optIndex: number) => {
        PLACEHOLDER_PATTERNS.genericOptions.forEach(pattern => {
          if (pattern.test(option.text || '')) {
            issues.push({
              type: 'generic_options',
              severity: 'high',
              description: 'Generic answer option detected',
              location: `Question ${index + 1}, Option ${optIndex + 1}`,
              suggestion: 'Replace with specific technical answer'
            });
          }
        });
      });
      
      // Check for placeholder scenarios
      PLACEHOLDER_PATTERNS.placeholderScenarios.forEach(pattern => {
        if (pattern.test(questionText)) {
          issues.push({
            type: 'placeholder_scenario',
            severity: 'medium',
            description: 'Placeholder scenario text found',
            location: `Question ${index + 1}`,
            suggestion: 'Replace with real-world cybersecurity scenario'
          });
        }
      });
    });
    
    // Calculate quality score (0-100)
    const totalChecks = questions.length * 4; // 4 checks per question
    const issueCount = issues.length;
    const qualityScore = Math.max(0, Math.round((1 - issueCount / totalChecks) * 100));
    
    // Determine if quiz needs replacement
    const highSeverityIssues = issues.filter(i => i.severity === 'high').length;
    const needsReplacement = highSeverityIssues > questions.length * 0.5; // More than 50% of questions have issues
    
    return {
      filename: filePath.split('/').pop() || '',
      quizId: metadata.quiz_id || 'unknown',
      title: metadata.title || 'Unknown Title',
      questionCount: questions.length,
      issues,
      qualityScore,
      needsReplacement
    };
    
  } catch (error) {
    console.error(`Error analyzing ${filePath}:`, error);
    return null;
  }
}

function generateReplacementPriority(results: QuizAuditResult[]): QuizAuditResult[] {
  return results
    .filter(r => r.needsReplacement)
    .sort((a, b) => {
      // Sort by severity and question count
      const aScore = a.issues.filter(i => i.severity === 'high').length / a.questionCount;
      const bScore = b.issues.filter(i => i.severity === 'high').length / b.questionCount;
      return bScore - aScore;
    });
}

async function comprehensivePlaceholderAudit() {
  console.log('🔍 Starting comprehensive placeholder content audit...\n');
  
  const dataDir = join(process.cwd(), 'src/data');
  const files = readdirSync(dataDir).filter(f => f.endsWith('.json'));
  
  const results: QuizAuditResult[] = [];
  
  for (const file of files) {
    const filePath = join(dataDir, file);
    const result = analyzeQuizFile(filePath);
    if (result) {
      results.push(result);
    }
  }
  
  // Generate summary statistics
  const totalQuizzes = results.length;
  const quizzesWithIssues = results.filter(r => r.issues.length > 0).length;
  const averageQuality = Math.round(results.reduce((sum, r) => sum + r.qualityScore, 0) / totalQuizzes);
  const needsReplacement = results.filter(r => r.needsReplacement).length;
  
  console.log('📊 Audit Summary:');
  console.log(`   Total quizzes analyzed: ${totalQuizzes}`);
  console.log(`   Quizzes with issues: ${quizzesWithIssues} (${Math.round(quizzesWithIssues/totalQuizzes*100)}%)`);
  console.log(`   Average quality score: ${averageQuality}%`);
  console.log(`   Quizzes needing replacement: ${needsReplacement}`);
  
  // Show high-priority replacements
  const priorityReplacements = generateReplacementPriority(results);
  
  if (priorityReplacements.length > 0) {
    console.log('\n🚨 HIGH PRIORITY REPLACEMENTS NEEDED:');
    priorityReplacements.slice(0, 10).forEach((quiz, index) => {
      const highIssues = quiz.issues.filter(i => i.severity === 'high').length;
      console.log(`\n${index + 1}. ${quiz.title}`);
      console.log(`   File: ${quiz.filename}`);
      console.log(`   Questions: ${quiz.questionCount}`);
      console.log(`   High severity issues: ${highIssues}`);
      console.log(`   Quality score: ${quiz.qualityScore}%`);
      
      // Show sample issues
      const sampleIssues = quiz.issues.slice(0, 3);
      sampleIssues.forEach(issue => {
        console.log(`   ⚠️  ${issue.description}: ${issue.location}`);
      });
    });
  }
  
  return {
    totalQuizzes,
    quizzesWithIssues,
    averageQuality,
    needsReplacement,
    priorityReplacements: priorityReplacements.slice(0, 20)
  };
}

// Run the audit
if (require.main === module) {
  comprehensivePlaceholderAudit()
    .then((summary) => {
      console.log('\n✅ Comprehensive placeholder audit completed!');
      console.log(`\n📋 Next Steps:`);
      console.log(`   1. Replace ${summary.needsReplacement} high-priority quizzes`);
      console.log(`   2. Focus on quizzes with generic numbering and template text`);
      console.log(`   3. Add real-world cybersecurity scenarios`);
      console.log(`   4. Improve technical specificity`);
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Error during audit:', error);
      process.exit(1);
    });
}

export { comprehensivePlaceholderAudit };
