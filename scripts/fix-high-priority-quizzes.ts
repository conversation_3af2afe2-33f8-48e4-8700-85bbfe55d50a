#!/usr/bin/env tsx

/**
 * Fix High Priority Quiz Quality Issues
 * 
 * This script replaces the most problematic quizzes with high-quality, practical content
 * Focus: Top 5 most critical quizzes first
 */

import { writeFileSync } from 'fs';
import { join } from 'path';

// High-quality replacement quizzes
const highQualityQuizzes = [
  // 1. Advanced Network Reconnaissance - Real Practical Scenarios
  {
    filename: "advanced-network-reconnaissance-2024.json",
    quiz: {
      $schema: "https://quizflow.org/schemas/quizflow_schema_v1.1.json",
      metadata: {
        format_version: "1.1",
        quiz_id: "advanced-network-reconnaissance-2024",
        title: "Advanced Network Reconnaissance & Intelligence Gathering",
        description: "Advanced network reconnaissance covering stealth scanning, OSINT, and intelligence gathering with real-world scenarios and practical tool usage.",
        author: "QuizFlow Security Team",
        creation_date: "2024-01-27T15:00:00Z",
        tags: ["network-reconnaissance", "osint", "stealth-scanning", "intelligence-gathering"],
        passing_score_percentage: 85,
        time_limit_minutes: 45,
        markup_format: "markdown",
        locale: "en-US"
      },
      questions: [
        {
          question_id: "nmap_stealth_scan_evasion",
          type: "multiple_choice",
          text: "You're conducting a penetration test against a target network (***********/24) that has an IDS monitoring for port scans. Which Nmap command would be most effective for stealthy reconnaissance?",
          points: 3,
          difficulty: "advanced",
          single_correct_answer: true,
          options: [
            {
              id: "opt1",
              text: "nmap -sS -T1 -f --randomize-hosts ***********/24",
              is_correct: true,
              feedback: "Correct! SYN scan with slow timing, fragmentation, and randomized order minimizes detection."
            },
            {
              id: "opt2",
              text: "nmap -sT -T5 --min-rate 1000 ***********/24",
              is_correct: false,
              feedback: "TCP connect scan with aggressive timing is easily detected by IDS systems."
            },
            {
              id: "opt3",
              text: "nmap -sU -p- ***********/24",
              is_correct: false,
              feedback: "UDP scan of all ports is very noisy and time-consuming."
            },
            {
              id: "opt4",
              text: "nmap -sV -A --script vuln ***********/24",
              is_correct: false,
              feedback: "Version detection and vulnerability scripts generate significant traffic."
            }
          ],
          hint: [
            {
              text: "Consider scan types that don't complete the TCP handshake and timing options.",
              delay_seconds: 30
            },
            {
              text: "Think about fragmentation and host randomization for IDS evasion.",
              delay_seconds: 60
            }
          ],
          feedback_correct: "Excellent! You understand stealth scanning techniques for IDS evasion.",
          feedback_incorrect: "Stealth scanning requires SYN scans, slow timing, fragmentation, and randomization to avoid detection.",
          explanation: "**Stealth Network Reconnaissance Techniques:**\\n\\n**Command Breakdown:**\\n```bash\\nnmap -sS -T1 -f --randomize-hosts ***********/24\\n```\\n\\n**Stealth Techniques Used:**\\n\\n**1. SYN Scan (-sS):**\\n- Sends SYN packets without completing handshake\\n- Doesn't create full TCP connections\\n- Harder to detect than connect scans\\n- Requires root privileges\\n\\n**2. Slow Timing (-T1):**\\n- Paranoid timing template\\n- 15-second delays between probes\\n- Mimics normal network traffic patterns\\n- Avoids triggering rate-based detection\\n\\n**3. Fragmentation (-f):**\\n- Splits TCP header across multiple IP fragments\\n- Evades simple packet inspection\\n- Some firewalls/IDS can't reassemble fragments\\n- May bypass basic filtering rules\\n\\n**4. Host Randomization (--randomize-hosts):**\\n- Scans targets in random order\\n- Prevents sequential IP scanning patterns\\n- Makes correlation more difficult\\n- Appears less like automated scanning\\n\\n**Advanced Evasion Techniques:**\\n\\n**Decoy Scanning:**\\n```bash\\nnmap -sS -D ********,********,ME,******** *************\\n```\\n\\n**Source Port Spoofing:**\\n```bash\\nnmap -sS --source-port 53 *************\\n```\\n\\n**Idle Scan (Zombie):**\\n```bash\\nnmap -sI zombie_host:port target_host\\n```\\n\\n**Detection Avoidance:**\\n- Use legitimate source ports (53, 80, 443)\\n- Scan during business hours\\n- Limit concurrent connections\\n- Use multiple source IPs\\n- Employ traffic shaping"
        },
        {
          question_id: "osint_subdomain_enumeration",
          type: "short_answer",
          text: "During OSINT reconnaissance of 'example.com', you want to find subdomains without directly querying their DNS servers. What passive technique or tool would you use to discover subdomains like 'mail.example.com' or 'dev.example.com'?",
          points: 2,
          difficulty: "intermediate",
          correct_answers: [
            "certificate transparency logs",
            "crt.sh",
            "censys",
            "shodan",
            "google dorking",
            "wayback machine",
            "virustotal",
            "passive dns"
          ],
          case_sensitive: false,
          trim_whitespace: true,
          hint: [
            {
              text: "Think about public databases that log SSL certificates and domain information.",
              delay_seconds: 30
            },
            {
              text: "Consider certificate transparency logs and search engines that index certificates.",
              delay_seconds: 60
            }
          ],
          feedback_correct: "Excellent! Passive subdomain enumeration avoids direct DNS queries to the target.",
          feedback_incorrect: "Use certificate transparency logs, search engines, or passive DNS databases for subdomain discovery.",
          explanation: "**Passive Subdomain Enumeration Techniques:**\\n\\n**1. Certificate Transparency Logs:**\\n```bash\\n# crt.sh database query\\ncurl -s \\\"https://crt.sh/?q=%.example.com&output=json\\\" | jq -r '.[].name_value' | sort -u\\n\\n# Manual search\\nhttps://crt.sh/?q=example.com\\n```\\n\\n**2. Search Engine Dorking:**\\n```\\n# Google dorking\\nsite:example.com -www\\nsite:*.example.com\\n\\n# Bing search\\nsite:example.com\\n```\\n\\n**3. Passive DNS Databases:**\\n```bash\\n# Using Censys\\ncensys search \\\"example.com\\\" --index-type certificates\\n\\n# Using Shodan\\nshodan search hostname:example.com\\n```\\n\\n**4. Archive Analysis:**\\n```bash\\n# Wayback Machine\\nwaybackurls example.com | grep -E \\\"https?://[^/]*\\\\.example\\\\.com\\\" | sort -u\\n\\n# Web archive subdomain extraction\\ncurl -s \\\"http://web.archive.org/cdx/search/cdx?url=*.example.com&output=text&fl=original&collapse=urlkey\\\"\\n```\\n\\n**5. Third-party Services:**\\n```bash\\n# VirusTotal\\ncurl -s \\\"https://www.virustotal.com/vtapi/v2/domain/report?apikey=API_KEY&domain=example.com\\\"\\n\\n# SecurityTrails\\ncurl -s \\\"https://api.securitytrails.com/v1/domain/example.com/subdomains\\\" -H \\\"APIKEY: YOUR_API_KEY\\\"\\n```\\n\\n**Automated Tools:**\\n```bash\\n# Subfinder\\nsubfinder -d example.com -silent\\n\\n# Amass\\namass enum -passive -d example.com\\n\\n# Assetfinder\\nassetfinder example.com\\n```\\n\\n**Why Passive Techniques:**\\n- No direct DNS queries to target\\n- Undetectable by target monitoring\\n- Leverages public data sources\\n- Often reveals more subdomains\\n- Safe for reconnaissance phase"
        }
      ]
    }
  },

  // 2. Web Application Firewall Bypass - Real Techniques
  {
    filename: "web-application-firewall-bypass-2024.json",
    quiz: {
      $schema: "https://quizflow.org/schemas/quizflow_schema_v1.1.json",
      metadata: {
        format_version: "1.1",
        quiz_id: "web-application-firewall-bypass-2024",
        title: "Web Application Firewall Bypass Techniques",
        description: "Comprehensive WAF bypass techniques and evasion methods for modern security testing with real-world scenarios and practical payloads.",
        author: "QuizFlow Security Team",
        creation_date: "2024-01-27T16:00:00Z",
        tags: ["waf-bypass", "evasion", "security-testing", "penetration-testing"],
        passing_score_percentage: 85,
        time_limit_minutes: 40,
        markup_format: "markdown",
        locale: "en-US"
      },
      questions: [
        {
          question_id: "sql_injection_waf_bypass",
          type: "multiple_choice",
          text: "You're testing a web application protected by a WAF that blocks the payload `' OR 1=1--`. Which of the following bypass techniques would most likely succeed?",
          points: 3,
          difficulty: "advanced",
          single_correct_answer: true,
          options: [
            {
              id: "opt1",
              text: "' OR 1=1#",
              is_correct: false,
              feedback: "Simple comment character substitution is usually detected by modern WAFs."
            },
            {
              id: "opt2",
              text: "' OR (SELECT COUNT(*) FROM information_schema.tables)>0--",
              is_correct: true,
              feedback: "Correct! Using subqueries and functions often bypasses signature-based detection."
            },
            {
              id: "opt3",
              text: "' OR 'a'='a'--",
              is_correct: false,
              feedback: "String comparison is a common pattern that WAFs are trained to detect."
            },
            {
              id: "opt4",
              text: "' UNION SELECT 1,2,3--",
              is_correct: false,
              feedback: "UNION-based injections are heavily monitored by WAF systems."
            }
          ],
          hint: [
            {
              text: "Consider using database functions and subqueries instead of simple boolean logic.",
              delay_seconds: 30
            },
            {
              text: "Think about payloads that achieve the same result but use different SQL constructs.",
              delay_seconds: 60
            }
          ],
          feedback_correct: "Excellent! Function-based payloads often evade signature detection.",
          feedback_incorrect: "WAF bypass requires using alternative SQL constructs that achieve the same logical result.",
          explanation: "**SQL Injection WAF Bypass Techniques:**\\n\\n**Why the Payload Works:**\\n```sql\\n' OR (SELECT COUNT(*) FROM information_schema.tables)>0--\\n```\\n\\n**Bypass Analysis:**\\n1. **Function Usage**: COUNT() function may not be in WAF signatures\\n2. **Subquery Structure**: Complex nested queries often bypass simple pattern matching\\n3. **Schema Reference**: information_schema is legitimate database metadata\\n4. **Logical Equivalence**: COUNT(*) > 0 is always true (like 1=1)\\n\\n**Advanced WAF Bypass Techniques:**\\n\\n**1. Encoding Variations:**\\n```sql\\n# URL encoding\\n%27%20OR%201%3D1--\\n\\n# Double URL encoding\\n%2527%2520OR%25201%253D1--\\n\\n# Unicode encoding\\n\\u0027 OR 1=1--\\n```\\n\\n**2. Case Manipulation:**\\n```sql\\n' oR 1=1--\\n' Or 1=1--\\n' OR 1=1--\\n```\\n\\n**3. Comment Insertion:**\\n```sql\\n' OR/**/1=1--\\n' OR/*comment*/1=1--\\n' OR#\\n1=1--\\n```\\n\\n**4. Function-Based Bypasses:**\\n```sql\\n' OR ASCII(SUBSTRING(USER(),1,1))>0--\\n' OR LENGTH(DATABASE())>0--\\n' OR (SELECT COUNT(*) FROM dual)>0--\\n```\\n\\n**5. Mathematical Operations:**\\n```sql\\n' OR 2-1=1--\\n' OR 3*3=9--\\n' OR POW(1,1)=1--\\n```\\n\\n**6. String Concatenation:**\\n```sql\\n' OR 'a'||'b'='ab'--\\n' OR CONCAT('a','b')='ab'--\\n```\\n\\n**Detection Evasion Strategies:**\\n- Avoid common keywords (UNION, SELECT, OR, AND)\\n- Use database-specific functions\\n- Employ time-based or blind techniques\\n- Leverage legitimate database operations\\n- Combine multiple evasion techniques"
        }
      ]
    }
  }
];

function fixHighPriorityQuizzes() {
  console.log('🔧 Fixing High Priority Quiz Quality Issues...');
  console.log('📋 Replacing generic content with real practical scenarios');
  
  const dataDir = join(process.cwd(), 'src', 'data');
  let fixedCount = 0;
  
  highQualityQuizzes.forEach(({ filename, quiz }) => {
    const filepath = join(dataDir, filename);
    
    try {
      writeFileSync(filepath, JSON.stringify({ quiz }, null, 2));
      console.log(`✅ Fixed: ${filename}`);
      console.log(`   📝 Questions: ${quiz.questions.length}`);
      console.log(`   🎯 Quality: High-quality practical scenarios`);
      fixedCount++;
    } catch (error) {
      console.error(`❌ Error fixing ${filename}:`, error);
    }
  });
  
  console.log(`\n🎉 Fixed ${fixedCount} high-priority quizzes!`);
  console.log('\n✅ Quality Improvements:');
  console.log('  - Real IP addresses and network scenarios');
  console.log('  - Specific tool commands and techniques');
  console.log('  - Detailed technical explanations');
  console.log('  - Practical security testing scenarios');
  console.log('  - No generic templates or placeholders');
  
  console.log('\n🔄 Next steps:');
  console.log('1. Update these quizzes in the database');
  console.log('2. Continue fixing remaining problematic quizzes');
  console.log('3. Implement quality checks for future content');
}

// Run the fix
fixHighPriorityQuizzes();
