#!/usr/bin/env tsx

/**
 * Seed 1000+ Questions to Database
 * 
 * This script seeds all newly generated practical quizzes to the database
 * with proper categorization, difficulty mapping, and comprehensive error handling
 */

import { PrismaClient } from '@/generated/prisma';
import { readFileSync, readdirSync } from 'fs';
import { join } from 'path';
import { QuizFlowJSON } from '@/types/qfjson';
import { getLocalizedText } from '@/lib/utils/qfjson-parser';
import bcrypt from 'bcrypt';

const prisma = new PrismaClient();

// Category mapping for auto-categorization
const categoryMapping: Record<string, string> = {
  'web-app': 'Web Application Security',
  'web-security': 'Web Application Security',
  'owasp': 'Web Application Security',
  'network-security': 'Network Security',
  'wireless': 'Network Security',
  'cloud-security': 'Cloud Security',
  'aws': 'Cloud Security',
  'azure': 'Cloud Security',
  'gcp': 'Cloud Security',
  'kubernetes': 'Cloud Security',
  'container': 'Cloud Security',
  'mobile-security': 'Mobile Security',
  'ios': 'Mobile Security',
  'android': 'Mobile Security',
  'malware-analysis': 'Malware Analysis',
  'reverse-engineering': 'Malware Analysis',
  'incident-response': 'Incident Response',
  'forensics': 'Incident Response',
  'cryptography': 'Cryptography & Encryption',
  'encryption': 'Cryptography & Encryption',
  'social-engineering': 'Social Engineering',
  'phishing': 'Social Engineering',
  'compliance': 'Compliance & Governance',
  'gdpr': 'Compliance & Governance',
  'governance': 'Compliance & Governance',
  'iot-security': 'Emerging Threats',
  'ai-security': 'Emerging Threats',
  'blockchain': 'Emerging Threats',
  'quantum': 'Emerging Threats',
  '5g': 'Emerging Threats'
};

// Difficulty mapping
const difficultyMapping: Record<string, string> = {
  'beginner': 'Beginner',
  'intermediate': 'Intermediate', 
  'advanced': 'Advanced'
};

async function seed1000Questions() {
  console.log('🚀 Starting 1000+ Questions Seeding Process...');
  console.log('📊 This will import all quiz files from src/data directory');

  try {
    // Ensure admin user exists
    const adminEmail = '<EMAIL>';
    let adminUser = await prisma.user.findUnique({
      where: { email: adminEmail }
    });

    if (!adminUser) {
      console.log('👤 Creating admin user...');
      const hashedPassword = await bcrypt.hash('admin123', 10);
      adminUser = await prisma.user.create({
        data: {
          email: adminEmail,
          name: 'QuizFlow Admin',
          password: hashedPassword,
          role: 'admin'
        }
      });
    }

    // Ensure categories exist
    const categoryNames = Array.from(new Set(Object.values(categoryMapping)));
    for (const categoryName of categoryNames) {
      await prisma.category.upsert({
        where: { name: categoryName },
        update: {},
        create: {
          name: categoryName,
          slug: categoryName.toLowerCase().replace(/[^a-z0-9]+/g, '-'),
          description: `${categoryName} related quizzes and challenges`
        }
      });
    }

    // Ensure difficulty levels exist
    const difficulties = [
      { name: 'Beginner', level: 1, description: 'Basic concepts and fundamentals', color: '#22c55e' },
      { name: 'Intermediate', level: 2, description: 'Practical application and analysis', color: '#f59e0b' },
      { name: 'Advanced', level: 3, description: 'Expert-level scenarios and techniques', color: '#ef4444' }
    ];

    for (const difficulty of difficulties) {
      await prisma.difficultyLevel.upsert({
        where: { name: difficulty.name },
        update: {},
        create: difficulty
      });
    }

    // Get all categories and difficulties
    const categories = await prisma.category.findMany();
    const difficultyLevels = await prisma.difficultyLevel.findMany();

    console.log(`📊 Found ${categories.length} categories and ${difficultyLevels.length} difficulty levels`);

    // Get all quiz files
    const dataDir = join(process.cwd(), 'src', 'data');
    const quizFiles = readdirSync(dataDir).filter(file => file.endsWith('.json'));
    
    console.log(`📁 Found ${quizFiles.length} quiz files to process`);

    let processedCount = 0;
    let skippedCount = 0;
    let totalQuestions = 0;
    let errorCount = 0;

    for (const filename of quizFiles) {
      const filePath = join(dataDir, filename);
      console.log(`\n📚 Processing ${filename}...`);

      try {
        // Read and parse the quiz file
        const fileContent = readFileSync(filePath, 'utf-8');
        const quizData: QuizFlowJSON = JSON.parse(fileContent);

        const metadata = quizData.quiz.metadata;
        const questions = quizData.quiz.questions || [];

        // Check if quiz already exists
        const existingQuiz = await prisma.quiz.findUnique({
          where: { quizId: metadata.quiz_id }
        });

        if (existingQuiz) {
          console.log(`⚠️  Quiz ${metadata.quiz_id} already exists, skipping...`);
          skippedCount++;
          continue;
        }

        // Auto-categorize based on tags
        let categoryId: string | null = null;
        if (metadata.tags) {
          for (const tag of metadata.tags) {
            const categoryName = categoryMapping[tag.toLowerCase()];
            if (categoryName) {
              const category = categories.find(c => c.name === categoryName);
              if (category) {
                categoryId = category.id;
                break;
              }
            }
          }
        }

        // Auto-assign difficulty based on quiz content or default to intermediate
        let difficultyId: string | null = null;
        const defaultDifficulty = difficultyLevels.find(d => d.name === 'Intermediate');
        if (defaultDifficulty) {
          difficultyId = defaultDifficulty.id;
        }

        // Create the quiz
        const quiz = await prisma.quiz.create({
          data: {
            quizId: metadata.quiz_id,
            title: getLocalizedText(metadata.title, metadata.locale),
            description: getLocalizedText(metadata.description, metadata.locale) || '',
            author: metadata.author || 'QuizFlow Security Team',
            tags: metadata.tags || [],
            passingScore: metadata.passing_score_percentage || 75,
            timeLimit: metadata.time_limit_minutes || 30,
            markupFormat: metadata.markup_format || 'markdown',
            locale: metadata.locale || 'en-US',
            formatVersion: metadata.format_version,
            isPublished: true,
            categoryId: categoryId,
            difficultyId: difficultyId,
            creatorId: adminUser.id,
            metadata: {
              sourceType: 'practical-generated',
              realWorldScenario: true,
              practicalExamples: true,
              generatedDate: new Date().toISOString(),
              batchImport: true
            }
          }
        });

        console.log(`✅ Created quiz: ${quiz.title}`);

        // Create questions
        if (questions.length > 0) {
          let createdQuestions = 0;
          
          for (const questionData of questions) {
            try {
              // Determine question difficulty
              let questionDifficultyId = difficultyId;
              if ((questionData as any).difficulty) {
                const difficultyName = difficultyMapping[(questionData as any).difficulty];
                if (difficultyName) {
                  const difficulty = difficultyLevels.find(d => d.name === difficultyName);
                  if (difficulty) {
                    questionDifficultyId = difficulty.id;
                  }
                }
              }

              await prisma.question.create({
                data: {
                  questionId: questionData.question_id,
                  type: questionData.type,
                  text: questionData.text,
                  points: questionData.points || 1,
                  feedbackCorrect: (questionData as any).feedback_correct || null,
                  feedbackIncorrect: (questionData as any).feedback_incorrect || null,
                  explanation: (questionData as any).explanation || null,
                  hint: (questionData as any).hint ? JSON.stringify((questionData as any).hint) : null,
                  options: (questionData as any).options ? JSON.stringify((questionData as any).options) : null,
                  correctAnswers: (questionData as any).correct_answers ? JSON.stringify((questionData as any).correct_answers) : null,
                  quizId: quiz.id,
                  difficultyId: questionDifficultyId,
                  realWorldScenario: true,
                  topicTags: metadata.tags || [],
                  sourceReference: `Generated from ${filename}`,
                  lastValidated: new Date()
                }
              });

              createdQuestions++;
            } catch (questionError) {
              console.error(`❌ Error creating question ${questionData.question_id}:`, questionError);
              errorCount++;
            }
          }

          console.log(`   📝 Created ${createdQuestions}/${questions.length} questions`);
          totalQuestions += createdQuestions;
        }

        processedCount++;

      } catch (error) {
        console.error(`❌ Error processing ${filename}:`, error);
        errorCount++;
      }
    }

    console.log('\n🎉 Seeding completed!');
    console.log('📊 Final Statistics:');
    console.log(`   ✅ Processed: ${processedCount} quizzes`);
    console.log(`   ⚠️  Skipped: ${skippedCount} quizzes (already existed)`);
    console.log(`   📝 Total Questions: ${totalQuestions}`);
    console.log(`   ❌ Errors: ${errorCount}`);

    if (totalQuestions >= 1000) {
      console.log('\n🏆 **MILESTONE ACHIEVED: 1000+ Questions Successfully Seeded!**');
    }

  } catch (error) {
    console.error('❌ Error during seeding:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding function
if (require.main === module) {
  seed1000Questions();
}

export default seed1000Questions;
