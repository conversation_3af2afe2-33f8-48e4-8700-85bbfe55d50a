#!/usr/bin/env tsx

/**
 * Update SOC Quiz in Database
 * 
 * This script updates the existing SOC quiz in the database with the corrected questions
 */

import { PrismaClient } from '@/generated/prisma';
import { readFileSync } from 'fs';
import { join } from 'path';

const prisma = new PrismaClient();

async function updateSOCQuizInDatabase() {
  console.log('🔄 Updating SOC Quiz in Database...');
  
  try {
    // Read the corrected quiz file
    const filePath = join(process.cwd(), 'src', 'data', 'security-operations-center-soc-2024.json');
    const fileContent = readFileSync(filePath, 'utf-8');
    const quizData = JSON.parse(fileContent);
    
    const metadata = quizData.quiz.metadata;
    const questions = quizData.quiz.questions;
    
    console.log(`📋 Processing quiz: ${metadata.title}`);
    console.log(`📝 Questions to update: ${questions.length}`);
    
    // Find the existing quiz
    const existingQuiz = await prisma.quiz.findUnique({
      where: { quizId: metadata.quiz_id },
      include: { questions: true }
    });
    
    if (!existingQuiz) {
      console.log('❌ Quiz not found in database');
      return;
    }
    
    console.log(`✅ Found existing quiz with ${existingQuiz.questions.length} questions`);
    
    // Delete existing questions
    console.log('🗑️  Deleting old questions...');
    await prisma.question.deleteMany({
      where: { quizId: existingQuiz.id }
    });
    
    // Add new questions
    console.log('➕ Adding corrected questions...');
    let addedCount = 0;
    
    for (const questionData of questions) {
      try {
        await prisma.question.create({
          data: {
            questionId: questionData.question_id,
            type: questionData.type,
            text: questionData.text,
            points: questionData.points || 1,
            feedbackCorrect: questionData.feedback_correct || null,
            feedbackIncorrect: questionData.feedback_incorrect || null,
            explanation: questionData.explanation || null,
            hint: questionData.hint ? JSON.stringify(questionData.hint) : null,
            options: questionData.options ? JSON.stringify(questionData.options) : null,
            correctAnswers: questionData.correct_answers ? JSON.stringify(questionData.correct_answers) : null,
            quizId: existingQuiz.id,
            difficultyId: existingQuiz.difficultyId,
            realWorldScenario: true,
            topicTags: metadata.tags || [],
            sourceReference: `Updated practical SOC scenarios`,
            lastValidated: new Date()
          }
        });
        
        addedCount++;
        console.log(`   ✅ Added: ${questionData.question_id}`);
        
      } catch (error) {
        console.error(`   ❌ Error adding question ${questionData.question_id}:`, error);
      }
    }
    
    console.log(`\n🎉 Successfully updated SOC quiz!`);
    console.log(`📊 Statistics:`);
    console.log(`   - Old questions removed: ${existingQuiz.questions.length}`);
    console.log(`   - New questions added: ${addedCount}`);
    console.log(`   - Quiz ID: ${existingQuiz.id}`);
    
    // Verify the update
    const updatedQuiz = await prisma.quiz.findUnique({
      where: { id: existingQuiz.id },
      include: { 
        questions: {
          select: {
            questionId: true,
            type: true,
            text: true
          }
        }
      }
    });
    
    console.log(`\n✅ Verification: Quiz now has ${updatedQuiz?.questions.length} questions`);
    
    if (updatedQuiz?.questions) {
      console.log('\n📝 Updated Questions:');
      updatedQuiz.questions.forEach((q, index) => {
        const preview = q.text.length > 80 ? q.text.substring(0, 80) + '...' : q.text;
        console.log(`   ${index + 1}. ${q.questionId} (${q.type})`);
        console.log(`      ${preview}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Error updating SOC quiz:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the update
if (require.main === module) {
  updateSOCQuizInDatabase();
}

export default updateSOCQuizInDatabase;
